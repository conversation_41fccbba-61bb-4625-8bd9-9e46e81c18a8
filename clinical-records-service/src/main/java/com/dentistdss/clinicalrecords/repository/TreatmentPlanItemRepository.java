package com.dentistdss.clinicalrecords.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.dentistdss.clinicalrecords.model.TreatmentPlanItem;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@Repository
public interface TreatmentPlanItemRepository extends JpaRepository<TreatmentPlanItem, Integer> {
    
    List<TreatmentPlanItem> findByTreatmentPlanIdOrderBySequenceOrder(Integer treatmentPlanId);
    
    List<TreatmentPlanItem> findByTreatmentPlanIdAndStatus(Integer treatmentPlanId, String status);
}
