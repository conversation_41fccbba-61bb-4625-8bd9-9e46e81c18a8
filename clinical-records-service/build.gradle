/*
 * Clinical Records Service - Build Script
 * Migrated from Maven pom.xml
 */

description = 'Clinical Records Service for DentistDSS'

dependencies {
    // Core Web and Data (both JPA and MongoDB as per Maven pom.xml)
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    runtimeOnly 'org.postgresql:postgresql'

    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // File Upload and Image Processing
    implementation 'commons-fileupload:commons-fileupload:1.5'
    implementation 'commons-io:commons-io:2.11.0'
    implementation 'net.coobird:thumbnailator:0.4.19'

    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui'
    }
}
