# =============================================================================
# Clinical Records Service Configuration - Development Profile
# =============================================================================

server:
  port: 8090

spring:
  application:
    name: clinical-records-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  datasource:
    url: *******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      idle-timeout: 300000
      connection-timeout: 20000
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: UTC
  data:
    mongodb:
      uri: mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@localhost:27017/dentistdss?authSource=admin
      database: dentistdss

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
    hostname: localhost

# =============================================================================
# OpenAPI/SpringDoc Configuration
# =============================================================================
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level:
    com.dentistdss: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# =============================================================================
# Management & Actuator Configuration
# =============================================================================
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info,refresh,env,configprops,metrics
  endpoint:
  security:
    enabled: false
    refresh:
      enabled: true
    health:
      show-details: always

# =============================================================================
# Feign Configuration
# =============================================================================
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: full

# =============================================================================
# File Upload Configuration
# =============================================================================
file:
  upload:
    max-file-size: 52428800  # 50MB
    max-request-size: 104857600  # 100MB
    allowed-image-types:
      - image/jpeg
      - image/png
      - image/tiff
      - image/bmp
    thumbnail-width: 200
    thumbnail-height: 200

# =============================================================================
# Application Specific Configuration
# =============================================================================
clinical-records:
  service:
    image-retention-days: 2555  # 7 years
    note-auto-save-interval: 30  # seconds
    max-note-versions: 10
