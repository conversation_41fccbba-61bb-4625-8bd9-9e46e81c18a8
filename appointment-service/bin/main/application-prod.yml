# =============================================================================
# Appointment Service Configuration - Production Profile
# =============================================================================

server:
  port: 8089

spring:
  application:
    name: appointment-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
  datasource:
    url: ${SPRING_DATASOURCE_URL:******************************************}
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        jdbc:
          time_zone: UTC
          batch_size: 25
        order_inserts: true
        order_updates: true
        batch_versioned_data: true

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://discovery-server:8761/eureka}
    fetch-registry: true
    register-with-eureka: true
    registry-fetch-interval-seconds: 30
  instance:
    preferIpAddress: true
    hostname: appointment-service
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# =============================================================================
# OpenAPI/SpringDoc Configuration - Production Profile (DISABLED)
# =============================================================================
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud.openfeign: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    org.springframework.security: WARN
    org.springframework.web: WARN
    com.netflix.eureka: WARN
    com.netflix.discovery: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/appointment-service.log
    max-size: 100MB
    max-history: 30

# =============================================================================
# Management & Actuator Configuration
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      sla:
        http.server.requests: 100ms, 500ms, 1s

# =============================================================================
# Feign Configuration
# =============================================================================
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 30000
        loggerLevel: basic
        retryer:
          period: 1000
          maxPeriod: 5000
          maxAttempts: 3
      notification-service:
        connectTimeout: 5000
        readTimeout: 15000
      auth-service:
        connectTimeout: 5000
        readTimeout: 10000
      clinic-service:
        connectTimeout: 5000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true

# =============================================================================
# Security Configuration
# =============================================================================
server:
  error:
    include-message: never
    include-binding-errors: never
    include-stacktrace: never
    include-exception: false

# =============================================================================
# Application Specific Configuration
# =============================================================================
appointment:
  service:
    default-appointment-duration: 30
    max-appointments-per-day: 20
    booking-advance-days: 90
    cancellation-deadline-hours: 24
    reminder-hours-before: 24
    cleanup:
      old-appointments-days: 365
      batch-size: 100
