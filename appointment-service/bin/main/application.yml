# =============================================================================
# Appointment Service Configuration - Development Profile
# =============================================================================

server:
  port: 8089

spring:
  application:
    name: appointment-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@localhost:8888
  datasource:
    url: *******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          time_zone: UTC

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# =============================================================================
# OpenAPI/SpringDoc Configuration - Development Profile
# =============================================================================
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level:
    com.dentistdss: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# =============================================================================
# Management & Actuator Configuration
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh
  endpoint:
    health:
      show-details: always

# =============================================================================
# Feign Configuration
# =============================================================================
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
