# =============================================================================
# Appointment Service Configuration - Docker Profile
# =============================================================================

server:
  port: 8089

spring:
  application:
    name: appointment-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
  datasource:
    url: ${SPRING_DATASOURCE_URL:******************************************}
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
        jdbc:
          time_zone: UTC

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://discovery-server:8761/eureka}
  instance:
    preferIpAddress: true
    hostname: appointment-service

# =============================================================================
# OpenAPI/SpringDoc Configuration - Docker Profile
# =============================================================================
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud.openfeign: INFO
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# =============================================================================
# Management & Actuator Configuration
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh
  endpoint:
    health:
      show-details: always

# =============================================================================
# Feign Configuration
# =============================================================================
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
