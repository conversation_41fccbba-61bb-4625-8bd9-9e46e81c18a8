/*
 * API Gateway - Build Script
 * Migrated from Maven pom.xml
 */

description = 'Spring Cloud Gateway Server'

dependencies {
    // Core Gateway
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway-server-webflux'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    // Configuration refresh support
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // Security and JWT
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    
    // JWT Token Processing
    implementation 'io.jsonwebtoken:jjwt-api'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson'

    // Rate Limiting
    implementation 'com.bucket4j:bucket4j_jdk17-core'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'

    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui'
    }

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.projectreactor:reactor-test'
}
