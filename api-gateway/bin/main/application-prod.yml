# =============================================================================
# API Gateway Configuration - Production Profile
# =============================================================================

server:
  port: 443
  ssl:
    enabled: true
    key-store: file:/app/certs/keystore.p12
    key-store-password: ${CA_STOREPASS}
    key-store-type: PKCS12
    key-alias: api-gateway

spring:
  application:
    name: api-gateway
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${AUTH_SERVICE_URL:http://auth-service:8081}/auth/oauth2/jwks
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  cloud:
    gateway:
      # =============================================================================
      # Global Configuration
      # =============================================================================
      server:
        webflux:
          globalcors:
            cors-configurations:
              '[/**]':
                allowedOrigins:
                  - "https://dentistdss.com"
                  - "https://accounts.google.com"
                allowedMethods:
                  - GET
                  - POST
                  - PUT
                  - DELETE
                  - OPTIONS
                  - PATCH
                  - HEAD
                allowedHeaders: "*"
                allowCredentials: true
                exposedHeaders: 
                  - "Authorization"
                  - "X-Session-ID"
                maxAge: 3600
          routes:
            # =============================================================================
            # Authentication & Authorization Services
            # =============================================================================

            # Authentication Service - JWT token management
            - id: auth-service
              uri: lb://auth-service
              predicates:
                - Path=/api/auth/**
              filters:
                - StripPrefix=1

            # OAuth2 endpoints - now handled by auth-service (OAuth2 standard endpoints)
            - id: auth-service-oauth2-standard-paths
              uri: lb://auth-service
              predicates:
                - Path=/login/oauth2/**, /oauth2/**
              filters:
                - StripPrefix=0

            # OAuth2 endpoints - now handled by auth-service (Custom API endpoints with /api prefix)
            - id: auth-service-oauth2-api
              uri: lb://auth-service
              predicates:
                - Path=/api/oauth2/**
              filters:
                - StripPrefix=1

            # =============================================================================
            # Business Domain Services
            # =============================================================================

            # Clinic Management Service
            - id: clinic-service
              uri: lb://clinic-service
              predicates:
                - Path=/api/clinic/**
              filters:
                - StripPrefix=1

            # Appointment Service
            - id: appointment-service
              uri: lb://appointment-service
              predicates:
                - Path=/api/appointment/**
              filters:
                - StripPrefix=1

            # Clinical Records Service
            - id: clinical-records-service
              uri: lb://clinical-records-service
              predicates:
                - Path=/api/clinical-records/**
              filters:
                - StripPrefix=1

            # User Profile Service (Patient and User Management)
            - id: user-profile-service-patient
              uri: lb://user-profile-service
              predicates:
                - Path=/api/patient/**
              filters:
                - StripPrefix=1

            # User Profile Service (User Management)
            - id: user-profile-service-user
              uri: lb://user-profile-service
              predicates:
                - Path=/api/user/**
              filters:
                - StripPrefix=1

            # User Profile Service (Dentist Management)
            - id: user-profile-service-dentist
              uri: lb://user-profile-service
              predicates:
                - Path=/api/dentist/**
              filters:
                - StripPrefix=1

            # AI/GenAI Service
            - id: genai-service
              uri: lb://genai-service
              predicates:
                - Path=/api/genai/**
              filters:
                - PreserveHostHeader
                - StripPrefix=1

            # =============================================================================
            # System & Infrastructure Services
            # =============================================================================

            # System Management Service
            - id: system-admin-service
              uri: lb://system-admin-service
              predicates:
                - Path=/api/system-admin/**
              filters:
                - StripPrefix=1

            # Audit & Logging Service
            - id: audit-service
              uri: lb://audit-service
              predicates:
                - Path=/api/audit/**
              filters:
                - StripPrefix=1

            # Notification Service
            - id: notification-service
              uri: lb://notification-service
              predicates:
                - Path=/api/notification/**
              filters:
                - StripPrefix=1

            # Chat Log Service
            - id: chat-log-service
              uri: lb://chat-log-service
              predicates:
                - Path=/api/chatlogs/**
              filters:
                - StripPrefix=1

            # Reporting Service
            - id: reporting-service
              uri: lb://reporting-service
              predicates:
                - Path=/api/reports/**
              filters:
                - StripPrefix=1

            # Admin Server (Spring Boot Admin) - Limited access in production
            - id: admin-server
              uri: lb://admin-server
              predicates:
                - Path=/admin, /admin/**
              filters:
                - StripPrefix=0

      # =============================================================================
      # Route Configuration - Production (No OpenAPI routes)
      # =============================================================================

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
rate-limit:
  config:
    service-url: http://system-admin-service:8086

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false
    securePortEnabled: true
    nonSecurePortEnabled: false
    securePort: 443

# =============================================================================
# Server Configuration
# =============================================================================
# Configure reactor netty server to use virtual threads if running on Java 21+
reactor:
  netty:
    http:
      server:
        accessLogEnabled: false  # Disabled in production for performance

# =============================================================================
# OpenAPI/SpringDoc Configuration - DISABLED for Production
# =============================================================================
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# =============================================================================
# Logging Configuration - Production
# =============================================================================
logging:
  level:
    com.dentistdss: INFO
    org.springframework: WARN
    org.springframework.cloud.gateway: INFO
    reactor.netty: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# =============================================================================
# Management & Actuator Configuration - Production
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh  # Limited endpoints in production
  endpoint:
    health:
      show-details: when-authorized  # Restricted health details
    gateway:
      access: none
