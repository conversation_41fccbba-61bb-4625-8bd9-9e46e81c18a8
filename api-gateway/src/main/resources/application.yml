# =============================================================================
# API Gateway Configuration - Development Profile
# =============================================================================

server:
  port: 8080

spring:
  application:
    name: api-gateway
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@localhost:8888
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${AUTH_SERVICE_URL:http://localhost:8081}/auth/oauth2/jwks
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  cloud:
    gateway:
      # =============================================================================
      # Global Configuration
      # =============================================================================
      server:
        webflux:
          default-filters:
            - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_FIRST
          globalcors:
            cors-configurations:
              '[/**]':
                allowedOrigins:
                  - "http://localhost:3000"
                  - "http://localhost:8080"
                  - "https://accounts.google.com"
                allowedMethods:
                  - GET
                  - POST
                  - PUT
                  - DELETE
                  - OPTIONS
                  - PATCH
                  - HEAD
                allowedHeaders: "*"
                allowCredentials: true
                exposedHeaders:
                  - "Authorization"
                  - "X-Session-ID"
                maxAge: 3600
          routes:
            # =============================================================================
            # Authentication & Authorization Services
            # =============================================================================

            # Authentication Service - JWT token management
            - id: auth-service
              uri: lb://auth-service
              predicates:
                - Path=/api/auth/**
              filters:
                - StripPrefix=1

            # OAuth2 endpoints - now handled by auth-service (OAuth2 standard endpoints)
            - id: auth-service-oauth2-standard-paths
              uri: lb://auth-service
              predicates:
                - Path=/login/oauth2/**, /oauth2/**
              filters:
                - StripPrefix=0

            # OAuth2 endpoints - now handled by auth-service (Custom API endpoints with /api prefix)
            - id: auth-service-oauth2-api
              uri: lb://auth-service
              predicates:
                - Path=/api/oauth2/**
              filters:
                - StripPrefix=1

            # =============================================================================
            # Business Domain Services
            # =============================================================================

            # Clinic Administration Service
            - id: clinic-admin-service
              uri: lb://clinic-admin-service
              predicates:
                - Path=/api/clinic-admin/**
              filters:
                - StripPrefix=1

            # Appointment Service
            - id: appointment-service
              uri: lb://appointment-service
              predicates:
                - Path=/api/appointment/**
              filters:
                - StripPrefix=1

            # Clinical Records Service
            - id: clinical-records-service
              uri: lb://clinical-records-service
              predicates:
                - Path=/api/clinical-records/**
              filters:
                - StripPrefix=1

            # User Profile Service (Patient and User Management)
            - id: user-profile-service-patient
              uri: lb://user-profile-service
              predicates:
                - Path=/api/patient/**
              filters:
                - StripPrefix=1

            # User Profile Service (User Management)
            - id: user-profile-service-user
              uri: lb://user-profile-service
              predicates:
                - Path=/api/user/**
              filters:
                - StripPrefix=1

            # User Profile Service (Dentist Management)
            - id: user-profile-service-dentist
              uri: lb://user-profile-service
              predicates:
                - Path=/api/dentist/**
              filters:
                - StripPrefix=1

            # AI/GenAI Service
            - id: genai-service
              uri: lb://genai-service
              predicates:
                - Path=/api/genai/**
              filters:
                - StripPrefix=1

            # =============================================================================
            # System & Infrastructure Services
            # =============================================================================

            # System Administration Service
            - id: system-admin-service
              uri: lb://system-admin-service
              predicates:
                - Path=/api/system-admin/**
              filters:
                - StripPrefix=1

            # Audit & Logging Service
            - id: audit-service
              uri: lb://audit-service
              predicates:
                - Path=/api/audit/**
              filters:
                - StripPrefix=1

            # Notification Service
            - id: notification-service
              uri: lb://notification-service
              predicates:
                - Path=/api/notification/**
              filters:
                - StripPrefix=1

            # Chat Log Service
            - id: chat-log-service
              uri: lb://chat-log-service
              predicates:
                - Path=/api/chatlogs/**
              filters:
                - StripPrefix=1

            # Reporting Service
            - id: reporting-service
              uri: lb://reporting-service
              predicates:
                - Path=/api/reports/**
              filters:
                - StripPrefix=1

            # Admin Server (Spring Boot Admin)
            - id: admin-server
              uri: lb://admin-server
              predicates:
                - Path=/admin, /admin/**
              filters:
                - StripPrefix=0

            # =============================================================================
            # OpenAPI Documentation Routes (Development Only)
            # =============================================================================

            # Authentication Service Documentation (includes OAuth functionality)
            - id: auth-service-openapi
              uri: lb://auth-service
              predicates:
                - Path=/auth-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Clinic Admin Service Documentation
            - id: clinic-admin-service-openapi
              uri: lb://clinic-admin-service
              predicates:
                - Path=/clinic-admin-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Appointment Service Documentation
            - id: appointment-service-openapi
              uri: lb://appointment-service
              predicates:
                - Path=/appointment-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Clinical Records Service Documentation
            - id: clinical-records-service-openapi
              uri: lb://clinical-records-service
              predicates:
                - Path=/clinical-records-service/v3/api-docs
              filters:
                - StripPrefix=1

            # User Profile Service Documentation
            - id: user-profile-service-openapi
              uri: lb://user-profile-service
              predicates:
                - Path=/user-profile-service/v3/api-docs
              filters:
                - StripPrefix=1

            # GenAI Service Documentation
            - id: genai-service-openapi
              uri: lb://genai-service
              predicates:
                - Path=/genai-service/v3/api-docs
              filters:
                - StripPrefix=1

            # System Admin Service Documentation
            - id: system-admin-service-openapi
              uri: lb://system-admin-service
              predicates:
                - Path=/system-admin-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Audit Service Documentation
            - id: audit-service-openapi
              uri: lb://audit-service
              predicates:
                - Path=/audit-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Notification Service Documentation
            - id: notification-service-openapi
              uri: lb://notification-service
              predicates:
                - Path=/notification-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Chat Log Service Documentation
            - id: chat-log-service-openapi
              uri: lb://chat-log-service
              predicates:
                - Path=/chat-log-service/v3/api-docs
              filters:
                - StripPrefix=1

            # Reporting Service Documentation
            - id: reporting-service-openapi
              uri: lb://reporting-service
              predicates:
                - Path=/reporting-service/v3/api-docs
              filters:
                - StripPrefix=1

      # =============================================================================
      # Route Configuration
      # =============================================================================

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
rate-limit:
  config:
    service-url: http://localhost:8086

# =============================================================================
# Service Discovery Configuration
# =============================================================================
eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# =============================================================================
# OpenAPI/SpringDoc Configuration - Development Profile
# =============================================================================
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
    groups:
      enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    config-url: /v3/api-docs/swagger-config
    urls:
      - name: api-gateway
        url: /v3/api-docs
      - name: auth-service (includes OAuth)
        url: /auth-service/v3/api-docs
      - name: clinic-admin-service
        url: /clinic-admin-service/v3/api-docs
      - name: appointment-service
        url: /appointment-service/v3/api-docs
      - name: clinical-records-service
        url: /clinical-records-service/v3/api-docs
      - name: genai-service
        url: /genai-service/v3/api-docs
      - name: user-profile-service
        url: /user-profile-service/v3/api-docs
      - name: system-admin-service
        url: /system-admin-service/v3/api-docs
      - name: audit-service
        url: /audit-service/v3/api-docs
      - name: notification-service
        url: /notification-service/v3/api-docs
      - name: chat-log-service
        url: /chat-log-service/v3/api-docs
      - name: reporting-service
        url: /reporting-service/v3/api-docs

# =============================================================================
# Server Configuration
# =============================================================================
# Configure reactor netty server to use virtual threads if running on Java 21+
reactor:
  netty:
    http:
      server:
        accessLogEnabled: true

# =============================================================================
# Logging Configuration
# =============================================================================
logging:
  level:
    com.dentistdss: DEBUG
    org.springframework: DEBUG
    org.springframework.cloud.gateway: DEBUG
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# =============================================================================
# Management & Actuator Configuration
# =============================================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway,refresh,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    gateway:
      access: READ_ONLY
    refresh:
      access: unrestricted
  security:
    enabled: false




