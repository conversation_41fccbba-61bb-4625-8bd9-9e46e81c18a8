package com.dentistdss.gateway.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.dentistdss.gateway.dto.RateLimitConfigDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Rate Limit Configuration Cache Service
 *
 * Manages cached rate limit configurations to break the circular dependency
 * between rate limiting components and Feign clients. Loads configurations
 * asynchronously after application startup and refreshes them periodically.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 */
@Slf4j
@Service
public class RateLimitConfigCacheService {

    @Autowired
    private RateLimitConfigService rateLimitConfigService;
    
    // Thread-safe cache for configurations
    private final Map<String, RateLimitConfigDto> configCache = new ConcurrentHashMap<>();
    private final AtomicBoolean cacheInitialized = new AtomicBoolean(false);
    
    /**
     * Initialize cache after application is fully ready
     * This ensures all beans are created and no circular dependencies occur
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeCache() {
        log.info("Initializing rate limit configuration cache...");
        // Use a separate thread to avoid any potential circular dependency issues
        new Thread(() -> {
            try {
                Thread.sleep(2000); // Wait 2 seconds for all beans to be fully initialized
                refreshCache();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Cache initialization interrupted");
            }
        }, "rate-limit-cache-init").start();
    }
    
    /**
     * Refresh cache periodically (every 5 minutes)
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void refreshCacheScheduled() {
        if (cacheInitialized.get()) {
            log.debug("Refreshing rate limit configuration cache...");
            refreshCache();
        }
    }
    
    /**
     * Find matching configuration from cache
     */
    public RateLimitConfigDto findMatchingConfig(String endpoint, String userRole, Long clinicId) {
        if (!cacheInitialized.get()) {
            log.warn("Configuration cache not yet initialized, returning null");
            return null;
        }
        
        // Try to find exact match first
        String exactKey = buildCacheKey(endpoint, userRole, clinicId);
        RateLimitConfigDto config = configCache.get(exactKey);
        if (config != null) {
            return config;
        }
        
        // Try to find best matching configuration
        return findBestMatch(endpoint, userRole, clinicId);
    }
    
    /**
     * Refresh the configuration cache
     */
    private void refreshCache() {
        try {
            // Use RateLimitConfigService instead of Feign client to avoid HttpMessageConverters issue
            List<RateLimitConfigDto> configurations = rateLimitConfigService.getActiveConfigurations();

            if (configurations != null && !configurations.isEmpty()) {
                Map<String, RateLimitConfigDto> newCache = new ConcurrentHashMap<>();

                for (RateLimitConfigDto config : configurations) {
                    if (Boolean.TRUE.equals(config.getActive())) {
                        String key = buildCacheKey(
                            config.getEndpointPattern(),
                            config.getUserRole(),
                            config.getClinicId()
                        );
                        newCache.put(key, config);
                    }
                }

                // Replace the entire cache atomically
                configCache.clear();
                configCache.putAll(newCache);
                cacheInitialized.set(true);

                log.info("Successfully refreshed rate limit configuration cache with {} configurations",
                        newCache.size());
            } else {
                log.warn("Failed to refresh rate limit configuration cache: no configurations returned");
            }
        } catch (Exception e) {
            log.error("Error refreshing rate limit configuration cache: {}", e.getMessage(), e);
            // Don't clear the cache on error - keep the last known good configuration
        }
    }
    
    /**
     * Find best matching configuration using pattern matching
     */
    private RateLimitConfigDto findBestMatch(String endpoint, String userRole, Long clinicId) {
        RateLimitConfigDto bestMatch = null;
        int bestScore = -1;
        
        for (RateLimitConfigDto config : configCache.values()) {
            int score = calculateMatchScore(config, endpoint, userRole, clinicId);
            if (score > bestScore) {
                bestScore = score;
                bestMatch = config;
            }
        }
        
        return bestMatch;
    }
    
    /**
     * Calculate match score for configuration
     */
    private int calculateMatchScore(RateLimitConfigDto config, String endpoint, String userRole, Long clinicId) {
        int score = 0;
        
        // Endpoint pattern matching (highest priority)
        if (config.getEndpointPattern() != null && endpoint.startsWith(config.getEndpointPattern())) {
            score += 100;
        }
        
        // User role matching
        if (config.getUserRole() != null && config.getUserRole().equals(userRole)) {
            score += 10;
        } else if (config.getUserRole() == null) {
            score += 1; // Generic role config
        }
        
        // Clinic ID matching
        if (config.getClinicId() != null && config.getClinicId().equals(clinicId)) {
            score += 10;
        } else if (config.getClinicId() == null) {
            score += 1; // Generic clinic config
        }
        
        return score;
    }
    
    /**
     * Build cache key for configuration
     */
    private String buildCacheKey(String endpoint, String userRole, Long clinicId) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(endpoint != null ? endpoint : "null")
                  .append(":")
                  .append(userRole != null ? userRole : "null")
                  .append(":")
                  .append(clinicId != null ? clinicId.toString() : "null");
        return keyBuilder.toString();
    }
    

    
    /**
     * Get cache status for monitoring
     */
    public boolean isCacheInitialized() {
        return cacheInitialized.get();
    }
    
    /**
     * Get cache size for monitoring
     */
    public int getCacheSize() {
        return configCache.size();
    }
    
    /**
     * Clear cache (for testing or manual refresh)
     */
    public void clearCache() {
        configCache.clear();
        cacheInitialized.set(false);
        log.info("Rate limit configuration cache cleared");
    }
}
