server:
  port: 8083

spring:
  application:
    name: clinic-admin-service
  config:
    import: optional:configserver:http://localhost:8888

  # Database Configuration (PostgreSQL)
  datasource:
    url: *******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true



eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,refresh,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    refresh:
      access: unrestricted

# OpenAPI/SpringDoc Configuration - enabled for development
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    # Configure Swagger UI to use API Gateway by default
    config-url: /v3/api-docs/swagger-config
    try-it-out-enabled: true



logging:
  level:
    '[com.dentistdss]': DEBUG
    '[org.springframework]': DEBUG