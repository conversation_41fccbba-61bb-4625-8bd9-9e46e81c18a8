server:
  port: 8083

spring:
  application:
    name: clinic-admin-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  datasource:
    url: ******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# Management/Actuator Configuration - Production
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh  # Limited exposure for production
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    refresh:
      access: unrestricted

# OpenAPI/SpringDoc Configuration - DISABLED for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    '[com.dentistdss]': INFO
    '[org.springframework]': INFO
    root: INFO
