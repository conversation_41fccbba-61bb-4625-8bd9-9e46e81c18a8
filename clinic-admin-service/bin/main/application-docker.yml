server:
  port: 8083

spring:
  application:
    name: clinic-admin-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  datasource:
    url: ******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,refresh,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    refresh:
      access: unrestricted

# OpenAPI/SpringDoc Configuration - enabled for docker development
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging:
  level:
    '[com.dentistdss]': INFO
    '[org.springframework]': INFO