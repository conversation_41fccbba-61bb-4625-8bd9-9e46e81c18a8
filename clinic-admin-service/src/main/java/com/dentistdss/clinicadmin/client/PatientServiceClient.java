package com.dentistdss.clinicadmin.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import com.dentistdss.clinicadmin.dto.PatientResponse;

import java.util.List;

/**
 * Feign client for user-profile-service (patient endpoints)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@FeignClient(name = "user-profile-service", path = "/patient")
public interface PatientServiceClient {
    
    @GetMapping("/list/all")
    List<PatientResponse> getAllPatients();
    
    @GetMapping("/{id}")
    PatientResponse getPatientById(@PathVariable("id") Long patientId);
}
