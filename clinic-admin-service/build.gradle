/*
 * Clinic Admin Service - Build Script
 * Refactored from clinic-service for focused clinic administration
 */

description = 'Clinic Administration Service for DentistDSS'

dependencies {
    // Core Web and Data
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    runtimeOnly 'org.postgresql:postgresql'
    
    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    
    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui'
    }

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}
