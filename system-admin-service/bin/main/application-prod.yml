server:
  port: 8086

spring:
  application:
    name: system-admin-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  datasource:
    url: ******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${AUTH_SERVICE_URL:http://auth-service:8081}/auth/oauth2/jwks

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# OpenAPI/SpringDoc Configuration - DISABLED for production

# Management/Actuator Configuration - Production
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh  # Limited exposure for production
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    refresh:
      access: unrestricted
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues
  security:
    enabled: true
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO
