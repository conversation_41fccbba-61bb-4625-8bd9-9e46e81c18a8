spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:dentistdss}
    username: ${DB_USERNAME:dentistdss}
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI}
  instance:
    preferIpAddress: true

# OpenAPI/SpringDoc Configuration - disabled for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    com.dentistdss: INFO
    org.springframework: WARN
    root: WARN
