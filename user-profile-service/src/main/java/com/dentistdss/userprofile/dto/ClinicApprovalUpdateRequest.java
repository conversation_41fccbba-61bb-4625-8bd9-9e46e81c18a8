package com.dentistdss.userprofile.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Request DTO for updating clinic approval status
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClinicApprovalUpdateRequest {
    private Boolean approved;
    private Long approvalBy;
    private LocalDateTime approvalDate;
    private Boolean enabled;
}
