# DentistDSS Microservices - Gradle Properties
# Migrated from Maven properties

# Project Information
group=com.dentistdss
version=0.9.2

# Java Configuration
java.version=21
sourceCompatibility=21
targetCompatibility=21

# Encoding
systemProp.file.encoding=UTF-8
systemProp.project.build.sourceEncoding=UTF-8
systemProp.project.reporting.outputEncoding=UTF-8

# Dependency Versions (migrated from Maven properties)
springBootVersion=3.5.0
springCloudVersion=2025.0.0
jjwtVersion=0.12.6
springBootAdminVersion=3.5.0
springdocOpenApiVersion=2.8.8
springAiVersion=1.0.0
googleApiClientVersion=2.8.0
googleHttpClientVersion=1.47.0
springCloudGcpVersion=6.2.1
googleCloudLibrariesVersion=26.61.0
bucket4jVersion=8.14.0
hypersistenceUtilsVersion=3.9.11

# Gradle Performance Optimizations
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError

# Build Performance
org.gradle.workers.max=8

# Kotlin DSL Performance (if needed later)
kotlin.code.style=official

# Profile Configuration (equivalent to Maven profiles)
# Default profile is 'dev', can be overridden with -Pprofile=prod or -Pprofile=docker
profile=dev
springdocEnabled=true
