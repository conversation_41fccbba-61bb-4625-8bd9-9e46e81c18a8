version: '3.8'

services:

  # PostgreSQL Database
  postgres:
    image: postgres:17
    container_name: postgres
    restart: always
    environment:
      POSTGRES_DB: dentistdss
      POSTGRES_USER: dentistdss
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
    networks:
      - dentistdss-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dentistdss"]
      interval: 10s
      timeout: 5s
      retries: 5

  
  # MongoDB Database
  mongo:
    image: mongo:7.0
    container_name: mongo
    restart: always
    environment:
      - MONGO_INITDB_DATABASE=dentistdss
      - MONGO_INITDB_ROOT_USERNAME=dentistdss
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - dentistdss-network
    healthcheck:
      test: ["CMD-SHELL", "mongosh --eval 'db.adminCommand(\"ping\")' --username dentistdss --password ${MONGO_INITDB_ROOT_PASSWORD} --authenticationDatabase admin"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:8.0.2
    container_name: redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dentistdss-network
    healthcheck:
      test: ["CMD-SHELL", "redis-cli --no-auth-warning -a ${REDIS_PASSWORD} ping | grep PONG"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Config Server
  config-server:
    build:
      context: ./config-server
    container_name: config-server
    restart: always
    ports:
      - "8888:8888"
    volumes:
      - ./config-server/src/main/resources/config:/app/config
    networks:
      - dentistdss-network
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - LOGGING_LEVEL_ROOT=DEBUG
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - GIT_REPO=${GIT_REPO}

  # Discovery Server (Eureka)
  discovery-server:
    build:
      context: ./discovery-server
    container_name: discovery-server
    restart: always
    ports:
      - "8761:8761"
    networks:
      - dentistdss-network
    depends_on:
      - config-server
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
    container_name: api-gateway
    restart: always
    ports:
      - "443:443"
    networks:
      - dentistdss-network
    depends_on:
      - discovery-server
      - redis
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - CA_STOREPASS=${CA_STOREPASS}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
    container_name: auth-service
    restart: always
    ports:
      - "8081:8081"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - redis
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - JWT_RSA_PRIVATE_KEY=${JWT_RSA_PRIVATE_KEY}
      - JWT_RSA_PUBLIC_KEY=${JWT_RSA_PUBLIC_KEY}
      - JWT_RSA_KEY_ID=${JWT_RSA_KEY_ID}
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}



  # Clinic Admin Service
  clinic-admin-service:
    build:
      context: ./clinic-admin-service
    container_name: clinic-admin-service
    restart: always
    ports:
      - "8083:8083"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Appointment Service
  appointment-service:
    build:
      context: ./appointment-service
    container_name: appointment-service
    restart: always
    ports:
      - "8089:8089"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Clinical Records Service
  clinical-records-service:
    build:
      context: ./clinical-records-service
    container_name: clinical-records-service
    restart: always
    ports:
      - "8090:8090"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
      - mongo
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGODB_URI=mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@mongo:27017/dentistdss_files?authSource=admin
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}

  # Admin Server
  admin-server:
    build:
      context: ./admin-server
    container_name: admin-server
    restart: always
    ports:
      - "9090:9090"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Notification Service
  notification-service:
    build:
      context: ./notification-service
    container_name: notification-service
    restart: always
    ports:
      - "8088:8088"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}

  # GenAI Service
  genai-service:
    build:
      context: ./genai-service
    container_name: genai-service
    restart: always
    ports:
      - "8084:8084"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - mongo
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - VERTEX_AI_PROJECT_ID=${VERTEX_AI_PROJECT_ID}
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
      - GEMINI_MODEL=${GEMINI_MODEL}
      - VERTEX_AI_ENABLED=${VERTEX_AI_ENABLED}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}

  # User Profile Service
  user-profile-service:
    build:
      context: ./user-profile-service
    container_name: user-profile-service
    restart: always
    ports:
      - "8085:8085"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # System Administration Service
  system-admin-service:
    build:
      context: ./system-admin-service
    container_name: system-admin-service
    restart: always
    ports:
      - "8086:8086"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Audit Service
  audit-service:
    build:
      context: ./audit-service
    container_name: audit-service
    restart: always
    ports:
      - "8087:8087"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - mongo
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATA_MONGODB_URI=mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@mongo:27017/dentistdss?authSource=admin
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}

  # Chat Log Service
  chat-log-service:
    build:
      context: ./chat-log-service
    container_name: chat-log-service
    restart: always
    ports:
      - "8091:8091"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - mongo
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATA_MONGODB_URI=mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@mongo:27017/dentistdss?authSource=admin
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}

  # Workflow Service
  workflow-service:
    build:
      context: ./workflow-service
    container_name: workflow-service
    restart: always
    ports:
      - "8093:8093"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - SPRING_DATASOURCE_URL=******************************************
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}

  # Reporting Service
  reporting-service:
    build:
      context: ./reporting-service
    container_name: reporting-service
    restart: always
    ports:
      - "8092:8092"
    networks:
      - dentistdss-network
    depends_on:
      - api-gateway
      - postgres
      - mongo
      - redis
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_CONFIG_IMPORT=optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
      - EUREKA_URI=http://discovery-server:8761/eureka
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_READONLY_PASSWORD=${POSTGRES_READONLY_PASSWORD}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SPRING_CONFIG_USER=${SPRING_CONFIG_USER}
      - SPRING_CONFIG_PASS=${SPRING_CONFIG_PASS}
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
    volumes:
      - reporting_temp:/app/temp
      - reporting_files:/app/reports

# Volumes
volumes:
  postgres_data:
    driver: local
  mongo_data:
    driver: local
  redis_data:
    driver: local
  reporting_temp:
    driver: local
  reporting_files:
    driver: local

# Networks
networks:
  dentistdss-network:
    driver: bridge 