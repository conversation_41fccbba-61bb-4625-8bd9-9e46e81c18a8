spring:
  application:
    name: reporting-service-test

  # Test MongoDB Configuration
  data:
    mongodb:
      database: test_dentistdss

  # Disable mail for tests
  mail:
    host: localhost
    port: 25
    username: test
    password: test

  # Quartz Configuration for Tests (use in-memory)
  quartz:
    job-store-type: memory

  # Security Configuration for Tests
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8081/auth/oauth2/jwks

# Cache Configuration for Tests
cache:
  caffeine:
    spec: maximumSize=100,expireAfterWrite=5m
  redis:
    ttl: 300

# Reporting Configuration for Tests
reporting:
  async:
    core-pool-size: 2
    max-pool-size: 5
    queue-capacity: 10
  export:
    temp-directory: ${java.io.tmpdir}/reporting-test
    max-file-size: 10MB
  email:
    from: <EMAIL>
    max-recipients: 10

logging:
  level:
    com.dentistdss: DEBUG
    org.springframework.data.jpa: DEBUG
    org.springframework.data.mongodb: DEBUG
    org.testcontainers: INFO
    root: INFO
