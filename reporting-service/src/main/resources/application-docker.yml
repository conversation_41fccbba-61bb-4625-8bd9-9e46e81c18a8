server:
  port: 8092

spring:
  application:
    name: reporting-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  # Primary Database (PostgreSQL)
  datasource:
    primary:
      url: ******************************************
      username: dentistdss
      password: ${POSTGRES_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        pool-name: ReportingHikariPool
    replica:
      url: ******************************************
      username: dentistdss
      password: ${POSTGRES_PASSWORD}
      driver-class-name: org.postgresql.Driver
      hikari:
        maximum-pool-size: 15
        minimum-idle: 3
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        pool-name: ReportingReplicaHikariPool

  # MongoDB for Report Metadata and Redis for Caching
  data:
    mongodb:
      uri: mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@mongo:27017/dentistdss?authSource=admin
      database: dentistdss
    # Redis for Caching
    redis:
      host: redis
      port: 6379
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms

  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://auth-service:8081/auth/oauth2/jwks

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  jmx:
    exposure:
      exclude: "*"

# OpenAPI/SpringDoc Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging:
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO
