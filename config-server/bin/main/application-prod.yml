spring:
  profiles:
    active: git
  cloud:
    config:
      server:
        git:
          uri: ${CONFIG_GIT_URI:https://github.com/zm377/dentistdss-microservices-config}
          default-label: main
          clone-on-start: true
          force-pull: true
          timeout: 15
          search-paths: '{application}'
          username: ${CONFIG_GIT_USERNAME:}
          password: ${CONFIG_GIT_PASSWORD:}
          # Production-specific Git settings
          refresh-rate: 60
        # Keep native as fallback
        native:
          search-locations: classpath:/config

# Production Actuator Configuration - Limited exposure
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    refresh:
      access: unrestricted
  security:
    enabled: true

# Eureka Client Configuration for Production
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI:http://discovery-server:8761/eureka}
  instance:
    prefer-ip-address: false
    hostname: ${HOSTNAME:config-server}

# OpenAPI/SpringDoc Configuration - DISABLED for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# Production logging - More restrictive
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud.config: WARN
    org.springframework.security: WARN
    org.eclipse.jgit: ERROR
    root: WARN
