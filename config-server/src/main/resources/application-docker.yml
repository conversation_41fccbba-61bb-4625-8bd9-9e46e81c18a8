spring:
  profiles:
    active: git
  cloud:
    config:
      server:
        git:
          uri: ${CONFIG_GIT_URI:https://github.com/zm377/dentistdss-microservices-config}
          default-label: main
          clone-on-start: true
          force-pull: true
          timeout: 10
          search-paths: '{application}'
          username: ${CONFIG_GIT_USERNAME:}
          password: ${CONFIG_GIT_PASSWORD:}
        # Keep native as fallback
        native:
          search-locations: classpath:/config

# Eureka Client Configuration for Docker
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI:http://discovery-server:8761/eureka}
  instance:
    prefer-ip-address: true
    hostname: config-server

# Docker-specific logging
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud.config: INFO
    org.eclipse.jgit: WARN
    root: INFO