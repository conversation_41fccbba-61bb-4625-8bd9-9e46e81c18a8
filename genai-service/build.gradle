/*
 * Generative AI Service - Build Script
 * Migrated from Maven pom.xml
 */

description = 'Generative AI Service using Spring AI'

dependencies {
    // WebFlux for streaming responses
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    
    // Spring AI with Google Vertex AI backend
    implementation 'org.springframework.ai:spring-ai-starter-model-vertex-ai-gemini'
    

    
    // MongoDB Reactive
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb-reactive'
    
    // Google Cloud Dependencies (BOM imports with versions)
    implementation platform("com.google.cloud:spring-cloud-gcp-dependencies:${springCloudGcpVersion}")
    implementation platform("com.google.cloud:libraries-bom:${googleCloudLibrariesVersion}")
    
    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui'
    }
}
