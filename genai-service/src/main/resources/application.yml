server:
  port: 8084

spring:
  application:
    name: genai-service
  config:
    import: optional:configserver:http://localhost:8888
  cloud:
    config:
      uri: http://localhost:8888 # Explicitly set for clarity with localhost
      username: ${SPRING_CONFIG_USER}
      password: ${SPRING_CONFIG_PASS}
      fail-fast: false
  data:
    mongodb:
      uri: mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@localhost:27017/dentistdss?authSource=admin
  ai:
    model:
      chat: vertexai
    # AI Provider Configuration
    vertex:
      ai:
        embedding:
          project-id: ${VERTEX_AI_PROJECT_ID:}
          location: ${VERTEX_AI_LOCATION:us-central1}
        gemini:
          project-id: ${VERTEX_AI_PROJECT_ID:}
          location: ${VERTEX_AI_LOCATION:us-central1}
          chat:
            options:
              model: ${GEMINI_MODEL:gemini-2.5-pro-preview-05-06}




# GenAI Provider Configuration
genai:
  providers:
    vertexai:
      enabled: ${VERTEX_AI_ENABLED:true}
      project-id: ${VERTEX_AI_PROJECT_ID:}
      location: ${VERTEX_AI_LOCATION:us-central1}
      default-model: ${GEMINI_MODEL:gemini-2.5-pro-preview-05-06}
      max-tokens: 4096
      temperature: 0.7

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,refresh,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    refresh:
      access: unrestricted
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues
  security:
    enabled: false

# OpenAPI/SpringDoc Configuration - enabled for development
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging:
  level:
    com.dentistdss: DEBUG
    org.springframework: DEBUG