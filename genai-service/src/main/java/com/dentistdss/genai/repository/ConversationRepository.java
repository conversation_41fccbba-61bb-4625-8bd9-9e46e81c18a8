package com.dentistdss.genai.repository;

import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import com.dentistdss.genai.model.Conversation;
import reactor.core.publisher.Flux;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
public interface ConversationRepository extends ReactiveMongoRepository<Conversation, String> {
    Flux<Conversation> findBySessionId(String sessionId);
} 