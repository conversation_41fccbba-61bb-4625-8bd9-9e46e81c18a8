server:
  port: 8084

spring:
  application:
    name: genai-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
  data:
    mongodb:
      uri: mongodb://dentistdss:${MONGO_INITDB_ROOT_PASSWORD}@mongo:27017/dentistdss?authSource=admin
  ai:
    model:
      chat: vertexai
    vertex:
      ai:
        embedding:
          project-id: ${VERTEX_AI_PROJECT_ID:}
          location: ${VERTEX_AI_LOCATION:us-central1}
        gemini:
          project-id: ${VERTEX_AI_PROJECT_ID:}
          location: ${VERTEX_AI_LOCATION:us-central1}
          chat:
            options:
              model: ${GEMINI_MODEL:gemini-2.5-pro-preview-05-06}

# GenAI Provider Configuration
genai:
  providers:
    vertexai:
      enabled: ${VERTEX_AI_ENABLED:true}
      project-id: ${VERTEX_AI_PROJECT_ID:}
      location: ${VERTEX_AI_LOCATION:us-central1}
      default-model: ${GEMINI_MODEL:gemini-2.5-pro-preview-05-06}
      max-tokens: 4096
      temperature: 0.7

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# OpenAPI/SpringDoc Configuration - DISABLED for production

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info  # Minimal exposure for production
  endpoint:
    health:
      show-details: when-authorized
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO
