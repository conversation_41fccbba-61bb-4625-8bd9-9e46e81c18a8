/*
 * Authentication Service - Build Script
 * Migrated from <PERSON>ven pom.xml
 */

description = 'Authentication Service for DentistDSS'

dependencies {
    // Core Web and Security
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    
    // Data Access
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    runtimeOnly 'org.postgresql:postgresql'
    
    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    
    // AOP
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    
    // JWT Token Processing
    implementation 'io.jsonwebtoken:jjwt-api'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson'
    
    // Google API Client for OAuth2 ID Token verification
    implementation 'com.google.api-client:google-api-client'
    implementation 'com.google.http-client:google-http-client-jackson2'
    
    // Test Dependencies
    testImplementation 'org.springframework.security:spring-security-test'
    
    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui'
    }
}
