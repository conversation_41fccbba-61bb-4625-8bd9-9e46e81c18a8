package com.dentistdss.auth.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.dentistdss.auth.model.User;
import com.dentistdss.auth.model.AuthProvider;
import com.dentistdss.auth.model.Role;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);

    Boolean existsByEmail(String email);

    Optional<User> findByEmailVerificationToken(String token);

    Optional<User> findByProviderIdAndProvider(String providerId, AuthProvider provider);

    @Query("SELECT u FROM User u JOIN u.roles r WHERE r = :role")
    List<User> findByRoles(@Param("role") Role role);

    @Query("SELECT u FROM User u JOIN u.roles r WHERE u.clinicId = :clinicId AND r = :role")
    List<User> findByClinicIdAndRoles(@Param("clinicId") Long clinicId, @Param("role") Role role);

    @Query("SELECT u FROM User u WHERE u.clinicId = :clinicId")
    List<User> findByClinicId(@Param("clinicId") Long clinicId);
}
