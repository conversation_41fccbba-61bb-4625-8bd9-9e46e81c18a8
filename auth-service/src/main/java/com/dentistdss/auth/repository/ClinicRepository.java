package com.dentistdss.auth.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.lang.NonNull;

import com.dentistdss.auth.model.Clinic;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@Repository
public interface ClinicRepository extends JpaRepository<Clinic, Long> {

    @NonNull
    Optional<Clinic> findById(@NonNull Long id);

    @NonNull
    Optional<Clinic> findByEmail(@NonNull String email);
}
