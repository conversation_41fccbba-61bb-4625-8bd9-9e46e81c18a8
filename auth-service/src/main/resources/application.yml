server:
  port: 8081

spring:
  application:
    name: auth-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@localhost:8888
  jmx:
    enabled: false


  # Database Configuration (PostgreSQL)
  datasource:
    url: *******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true

  # OAuth2 Configuration
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID} # To be overridden by config server
            client-secret: ${GOOGLE_CLIENT_SECRET} # To be overridden by config server
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}" # Spring will resolve baseUrl
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/v2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            user-name-attribute: sub



eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# JWT Configuration
jwt:
  expiration: ******** # 24 hours in milliseconds
  rsa:
    private-key: ${JWT_RSA_PRIVATE_KEY:}
    public-key: ${JWT_RSA_PUBLIC_KEY:}
    key-id: ${JWT_RSA_KEY_ID:dentistdss}

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues

# OpenAPI/SpringDoc Configuration - enabled for development
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging:
  level:
    com.dentistdss: DEBUG
    org.springframework: DEBUG
    root: DEBUG

# App Configuration
app:
  email-verification:
    token-expiry-minutes: 43200 # 30 days in minutes
    code-expiry-minutes: 10 # 10 minutes in minutes
    base-url: ${BASE_URL:http://localhost:3000}
  oauth2:
    authorizedRedirectUris:
      - https://dentistdss.com/oauth2/redirect
      - http://localhost:3000/oauth2/redirect # Default for local React app