server:
  port: 8081

spring:
  application:
    name: auth-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888
  jmx:
    enabled: false

  # Database Configuration (PostgreSQL)
  datasource:
    url: ******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false

  # OAuth2 Configuration
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID}
            client-secret: ${GOOGLE_CLIENT_SECRET}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/v2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            user-name-attribute: sub

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

# JWT Configuration
jwt:
  expiration: ******** # 24 hours in milliseconds
  rsa:
    private-key: ${JWT_RSA_PRIVATE_KEY:}
    public-key: ${JWT_RSA_PUBLIC_KEY:}
    key-id: ${JWT_RSA_KEY_ID:dentistdss}

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info  # Minimal exposure for production
  endpoint:
    health:
      show-details: when-authorized
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues

# OpenAPI/SpringDoc Configuration - DISABLED for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO

# App Configuration
app:
  email-verification:
    token-expiry-minutes: 43200 # 30 days in minutes
    code-expiry-minutes: 10 # 10 minutes in minutes
    base-url: ${BASE_URL:https://dentistdss.com}
  oauth2:
    authorizedRedirectUris:
      - https://dentistdss.com/oauth2/redirect
