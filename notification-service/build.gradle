/*
 * Notification Service - Build Script
 * Migrated from Maven pom.xml
 */

description = 'Notification Service for DentistDSS'

dependencies {
    // Core WebFlux and Data (as per Maven pom.xml)
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    runtimeOnly 'org.postgresql:postgresql'

    // Hypersistence Utils for JSON handling
    implementation 'io.hypersistence:hypersistence-utils-hibernate-63'

    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui'
    }
}
