server:
  port: 8088

spring:
  application:
    name: notification-service
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

  datasource:
    url: ******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false

  # Mail Configuration
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# OpenAPI/SpringDoc Configuration - DISABLED for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

eureka:
  client:
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka
  instance:
    preferIpAddress: false

logging:

# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info  # Minimal exposure for production
  endpoint:
    health:
      show-details: when-authorized
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO

# App Configuration
app:
  email-verification:
    token-expiry-minutes: 43200 # 30 days in minutes
    code-expiry-minutes: 10 # 10 minutes in minutes
    base-url: ${BASE_URL:https://dentistdss.com}
  email:
    from-email: ${MAIL_USERNAME}
