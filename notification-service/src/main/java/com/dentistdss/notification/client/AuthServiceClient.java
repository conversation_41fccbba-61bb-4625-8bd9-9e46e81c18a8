package com.dentistdss.notification.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@FeignClient(name = "auth-service", path = "/auth")
public interface AuthServiceClient {
    
    @GetMapping("/user/{id}/email")
    String getUserEmail(@PathVariable("id") Long userId);
    
    @GetMapping("/user/{id}/name")
    String getUserFullName(@PathVariable("id") Long userId);
} 