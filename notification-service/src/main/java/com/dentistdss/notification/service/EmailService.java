package com.dentistdss.notification.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import com.dentistdss.notification.client.AuthServiceClient;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @github https://github.com/zm377
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final JavaMailSender mailSender;
    private final AuthServiceClient authServiceClient;

    @Value("${app.email.from-email:${spring.mail.username}}")
    private String fromEmail;
    
    public void sendEmail(Long userId, String subject, String body) {
        try {
            String userEmail = authServiceClient.getUserEmail(userId);
            sendEmail(userEmail, subject, body, false);
        } catch (Exception e) {
            log.error("Failed to fetch email for user {}: {}", userId, e.getMessage());
            // Fallback for development/testing
            String fallbackEmail = "user" + userId + "@example.com";
            log.warn("Using fallback email {} for user {}", fallbackEmail, userId);
            sendEmail(fallbackEmail, subject, body, false);
        }
    }
    
    public void sendEmail(String to, String subject, String body, boolean isHtml) {
        try {
            if (isHtml) {
                sendHtmlEmail(to, subject, body);
            } else {
                sendSimpleEmail(to, subject, body);
            }
            log.info("Email sent successfully to {}", to);
        } catch (Exception e) {
            log.error("Failed to send email to {}: {}", to, e.getMessage());
            throw new RuntimeException("Failed to send email", e);
        }
    }
    
    private void sendSimpleEmail(String to, String subject, String body) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        message.setSubject(subject);
        message.setText(body);
        message.setFrom("<EMAIL>");
        
        mailSender.send(message);
    }
    
    private void sendHtmlEmail(String to, String subject, String body) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(body, true);
        helper.setFrom(fromEmail != null ? fromEmail : "<EMAIL>");

        mailSender.send(message);
    }

    // ===== Auth Service Email Methods =====

    @Async
    public void sendVerificationEmail(String to, String emailVerificationToken) {
        try {
            sendAVerificationEmail(to, emailVerificationToken, "token");
        } catch (MessagingException e) {
            log.error("Failed to send verification email to {}: {}", to, e.getMessage());
            throw new RuntimeException("Could not send verification email", e);
        }
    }

    @Async
    public void sendVerificationCode(String to, String verificationCode) {
        try {
            sendAVerificationEmail(to, verificationCode, "code");
        } catch (MessagingException e) {
            log.error("Failed to send verification code to {}: {}", to, e.getMessage());
            throw new RuntimeException("Could not send verification code", e);
        }
    }

    private void sendAVerificationEmail(String to, String vString, String type) throws MessagingException {
        String subject = "Complete Your Registration - Dentabot";
        String plainTextContent = String.format(
                "Hi dear user!\n\n" +
                        "This is an email auto-generated by the Dentabot system because you are signing up for an account.\n\n"
                        +
                        "Your verification %s is: %s. Which will expire in %s.\n\n" +
                        "If it's not you, please ignore this email.\n\n" +
                        "Thanks,\n" +
                        "The DentistDSS Team",
                type, vString, type.equals("code") ? "10 minutes" : "30 days");
        sendEmailInternal(to, subject, plainTextContent);
    }

    @Async
    public void sendProcessingReminderEmail(String clinicAdminEmail, String clinicName, String firstName, String lastName, String email, String role) {
        try {
            sendAProveRequestEmail(clinicAdminEmail, email, firstName, lastName, role, clinicName);
        } catch (MessagingException e) {
            log.error("Failed to send processing reminder email to {}: {}", email, e.getMessage());
            throw new RuntimeException("Could not send processing reminder email", e);
        }
    }

    private void sendAProveRequestEmail(String to, String email, String firstName, String lastName, String role,
            String clinicName) throws MessagingException {
        String subject = "Approve a new staff registration";
        String content = String.format(
                "Hi dear Admin!\n\n" +
                        "This is an email auto-generated by the Dentabot system.\n\n" +
                        "We sent you this email because there is a new clinic staff %s %s, %s.\n\n" +
                        "Who is requesting to register as a %s in clinic: %s.\n\n" +
                        "Please approve the request in the Dentabot system.\n\n" +
                        "Thanks,\n" +
                        "The DentistDSS Team",
                firstName, lastName, email, role, clinicName);
        sendEmailInternal(to, subject, content);
    }

    @Async
    public void sendSystemAdminApprovalEmail(String systemAdminEmail, String firstName, String lastName, String email,
            String clinicName, String address, String city, String state, String zipCode, String country,
            String phoneNumber, String businessEmail) {
        try {
            sendAProveRequestEmail(systemAdminEmail, email, firstName, lastName, "System Admin", clinicName);
        } catch (MessagingException e) {
            log.error("Failed to send system admin approval email to {}: {}", email, e.getMessage());
            throw new RuntimeException("Could not send system admin approval email", e);
        }
    }

    public void sendNotificationEmail(String to, String templateName, Map<String, String> variables) {
        MimeMessage message = mailSender.createMimeMessage();

        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(to);

            // Map template names to subjects and bodies
            String subject = getSubjectForTemplate(templateName);
            String body = getBodyForTemplate(templateName, variables);

            helper.setSubject(subject);
            helper.setText(body, false);
            helper.setFrom(fromEmail != null ? fromEmail : "<EMAIL>");

            mailSender.send(message);
            log.info("Notification email sent to {} using template {}", to, templateName);
        } catch (MessagingException e) {
            log.error("Failed to send notification email to {}: {}", to, e.getMessage());
            throw new RuntimeException("Failed to send notification email", e);
        }
    }

    private String getSubjectForTemplate(String templateName) {
        switch (templateName) {
            case "user_approval_request":
                return "New User Registration Requires Approval";
            case "user_approval_result":
                return "Your Registration Status Update";
            default:
                return "Notification from DentistDSS";
        }
    }

    private String getBodyForTemplate(String templateName, Map<String, String> variables) {
        String template = "";

        switch (templateName) {
            case "user_approval_request":
                template = "A new {{role}} registration from {{user_name}} requires your approval.\n" +
                        "Please log in to the system to review this request.\n" +
                        "Thanks,\n" +
                        "The DentistDSS Team";
                break;
            case "user_approval_result":
                template = "Dear {{user_name}},\n" +
                        "Your registration as {{role}} has been {{status}}.\n" +
                        "{{reason}}\n" +
                        "Thanks,\n" +
                        "The DentistDSS Team";
                break;
            default:
                template = "This is a notification from DentistDSS.";
        }

        // Replace variables in template
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            template = template.replace("{{" + entry.getKey() + "}}", entry.getValue());
        }

        return template;
    }

    private void sendEmailInternal(String to, String subject, String content) throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setFrom(fromEmail != null ? fromEmail : "<EMAIL>");
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(content, false);

        mailSender.send(message);
        log.info("Email sent to {}", to);
    }
}