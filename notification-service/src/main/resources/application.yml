server:
  port: 8088

spring:
  application:
    name: notification-service
  config:
    import: optional:configserver:http://localhost:8888

  datasource:
    url: *******************************************
    username: dentistdss
    password: ${POSTGRES_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true

  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false

# Management/Actuator Configuration
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info,refresh,env,configprops,metrics
  endpoint:
  security:
    enabled: false
    refresh:
      enabled: true
    health:
      show-details: always
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues

# OpenAPI/SpringDoc Configuration - enabled for development
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging:
  level:
    com.dentistdss: DEBUG

# App Configuration
app:
  email-verification:
    token-expiry-minutes: 43200 # 30 days in minutes
    code-expiry-minutes: 10 # 10 minutes in minutes
    base-url: ${BASE_URL:http://localhost:3000}
  email:
    from-email: ${MAIL_USERNAME}