server:
  port: 9090
  servlet:
    context-path: /admin

spring:
  application:
    name: admin-server
  config:
    import: optional:configserver:http://localhost:8888

  boot:
    admin:
      discovery:
        enabled: true
      ui:
        title: DentistDSS Admin

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  jmx:
    exposure:
      exclude: "*"  # Disable JMX to avoid RMI issues

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_URI:http://localhost:8761/eureka}
  instance:
    preferIpAddress: false 