/*
 * Audit Service - Build Script
 * Migrated from Maven pom.xml
 */

description = 'Audit Service for DentistDSS'

dependencies {
    // Core WebFlux and MongoDB (as per Maven pom.xml)
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

    // Cloud Services
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'

    // Profile-based OpenAPI dependency
    if (project.ext.springdocEnabled) {
        implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui'
    }
}
