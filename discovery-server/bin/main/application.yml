server:
  port: 8761

spring:
  application:
    name: discovery-server
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@localhost:8888


eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: false
    serviceUrl:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/


# Configure logging for components
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
# Management/Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh,env,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    refresh:
      access: unrestricted
  security:
    enabled: false
