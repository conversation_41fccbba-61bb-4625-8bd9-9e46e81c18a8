server:
  port: 8761

spring:
  application:
    name: discovery-server
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

eureka:
  instance:
    hostname: discovery-server
    preferIpAddress: false
  client:
    registerWithEureka: false
    fetchRegistry: false
    serviceUrl:
      defaultZone: http://discovery-server:8761/eureka/

# OpenAPI/SpringDoc Configuration - DISABLED for production
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

logging:
  level:
    com.dentistdss: INFO
    org.springframework: INFO
    root: INFO
