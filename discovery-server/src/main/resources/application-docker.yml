server:
  port: 8761

spring:
  application:
    name: discovery-server
  config:
    import: optional:configserver:http://${SPRING_CONFIG_USER}:${SPRING_CONFIG_PASS}@config-server:8888

eureka:
  instance:
    hostname: discovery-server
  client:
    registerWithEureka: false
    fetchRegistry: false
    serviceUrl:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/

# Configure logging for components
logging:
  level:
    com.dentistdss: INFO
    org.springframework.cloud: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
