HELP.md

# =============================================================================
# Build System Support (Gradle)
# Project has been migrated from Maven to Gradle
# =============================================================================

# Gradle build directories and files
build/
**/build/
!**/src/main/**/build/
!**/src/test/**/build/
.gradle/
**/.gradle/
!gradle/wrapper/gradle-wrapper.jar

# Gradle daemon and cache
gradle-app.setting

# Project-specific files
privkey.pem
fullchain.pem
generate-ssl-key.sh
lessons.txt
logs/
.env
.env.local
.env.*.local
*.env
c.sh

# Security-sensitive files
secrets/
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore

# Migration and documentation temporary files
*_MIGRATION_*.md
*_COMPLETION_*.md
*_SUMMARY_*.md
*_QUICK_REFERENCE*.md
*_UPDATE_SUMMARY*.md
migration-*.log

# Legacy Maven directories (no longer used after migration)
target/
**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Gradle Additional ###
# Gradle build scan cache
.gradle/buildOutputCleanup/
.gradle/vcs-1/

# Gradle wrapper (keep the jar but ignore other wrapper files if needed)
# gradle/wrapper/gradle-wrapper.properties (uncomment if you want to ignore wrapper properties)

# Gradle build reports and test results
**/build/reports/
**/build/test-results/
**/build/jacoco/

# Gradle temporary files
**/build/tmp/

# Gradle build cache directory (local cache)
.gradle/build-cache/

# Gradle performance and profiling
.gradle/profile/
gradle-profiler/
build-profile-*.html

# Gradle build scans (if generated locally)
build-scan-data/

# Gradle init scripts (if any)
init.gradle.kts
init.gradle

### macOS ###
.DS_Store

### Windows ###
Thumbs.db
ehthumbs.db
Desktop.ini

### Linux ###
*~
